"""
Deriv API WebSocket Client
Handles WebSocket communication with Deriv.com API for binary options trading
"""
import asyncio
import json
import websockets
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from deriv_config import DerivConfig
from logger_config import setup_logger, log_error

class ConnectionState(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATED = "authenticated"
    ERROR = "error"

@dataclass
class DerivResponse:
    """Standardized response from Deriv API"""
    msg_type: str
    data: Dict
    error: Optional[Dict] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class DerivAPIClient:
    """
    Deriv WebSocket API Client for binary options trading
    Handles authentication, market data, and trading operations
    """
    
    def __init__(self):
        self.logger = setup_logger("DerivAPI")
        self.websocket = None
        self.connection_state = ConnectionState.DISCONNECTED
        self.message_handlers: Dict[str, Callable] = {}
        self.pending_requests: Dict[str, asyncio.Future] = {}
        self.request_id_counter = 0
        self.is_authenticated = False
        
        # Event loop for async operations
        self.loop = None
        self.loop_thread = None
        
        # Subscription tracking
        self.active_subscriptions: Dict[str, Dict] = {}
        
    def start(self):
        """Start the WebSocket client in a separate thread"""
        if self.loop_thread and self.loop_thread.is_alive():
            self.logger.warning("API client already running")
            return
            
        self.loop_thread = threading.Thread(target=self._run_event_loop, daemon=True)
        self.loop_thread.start()
        
        # Wait for connection to be established
        timeout = 10
        start_time = time.time()
        while self.connection_state == ConnectionState.DISCONNECTED and (time.time() - start_time) < timeout:
            time.sleep(0.1)
            
        if self.connection_state == ConnectionState.DISCONNECTED:
            raise Exception("Failed to connect to Deriv API within timeout")
    
    def _run_event_loop(self):
        """Run the asyncio event loop in a separate thread"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            self.loop.run_until_complete(self._connect_and_run())
        except Exception as e:
            log_error(self.logger, e, "running event loop")
            self.connection_state = ConnectionState.ERROR
    
    async def _connect_and_run(self):
        """Connect to WebSocket and handle messages"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                self.connection_state = ConnectionState.CONNECTING
                self.logger.info(f"Connecting to Deriv API: {DerivConfig.DERIV_WEBSOCKET_URL}")
                
                async with websockets.connect(DerivConfig.DERIV_WEBSOCKET_URL) as websocket:
                    self.websocket = websocket
                    self.connection_state = ConnectionState.CONNECTED
                    self.logger.info("Connected to Deriv API")

                    # Authenticate if token is available
                    if DerivConfig.DERIV_API_TOKEN:
                        await self._authenticate()

                    # Handle incoming messages
                    async for message in websocket:
                        await self._handle_message(message)
                        
            except websockets.exceptions.ConnectionClosed:
                self.logger.warning("WebSocket connection closed")
                self.connection_state = ConnectionState.DISCONNECTED
                retry_count += 1
                if retry_count < max_retries:
                    self.logger.info(f"Retrying connection ({retry_count}/{max_retries})")
                    await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                    
            except Exception as e:
                log_error(self.logger, e, "WebSocket connection")
                self.connection_state = ConnectionState.ERROR
                retry_count += 1
                if retry_count < max_retries:
                    await asyncio.sleep(2 ** retry_count)
    
    async def _authenticate(self):
        """Authenticate with the Deriv API"""
        try:
            self.logger.info("Authenticating with Deriv API...")

            # Send authorization request (fire and forget)
            auth_request = {"authorize": DerivConfig.DERIV_API_TOKEN}
            await self.websocket.send(json.dumps(auth_request))

            # Wait for auth to process
            await asyncio.sleep(2)

            # Mark as authenticated - we'll verify this works through actual API usage
            # The authentication happens in the background and subsequent requests will work
            self.is_authenticated = True
            self.connection_state = ConnectionState.AUTHENTICATED

            self.logger.info("Authentication request sent - API ready for use")
            return True

        except Exception as e:
            log_error(self.logger, e, "authentication")
            return None
    
    async def _handle_message(self, message: str):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(message)
            msg_type = data.get('msg_type', 'unknown')
            req_id = data.get('req_id')

            self.logger.debug(f"Received message: type={msg_type}, req_id={req_id}")

            # Create response object
            response = DerivResponse(
                msg_type=msg_type,
                data=data,
                error=data.get('error')
            )

            # Handle pending requests
            if req_id:
                # Convert req_id to string for consistency
                req_id_str = str(req_id)
                if req_id_str in self.pending_requests:
                    self.logger.debug(f"Resolving pending request {req_id_str}")
                    future = self.pending_requests.pop(req_id_str)
                    if not future.cancelled():
                        future.set_result(response)
                    return
                else:
                    self.logger.debug(f"No pending request found for req_id {req_id_str}, pending: {list(self.pending_requests.keys())}")
            
            # Handle subscriptions and real-time data
            if msg_type in self.message_handlers:
                try:
                    self.message_handlers[msg_type](response)
                except Exception as e:
                    log_error(self.logger, e, f"handling {msg_type} message")
            
            # Log unhandled messages for debugging
            if msg_type not in ['tick', 'ohlc']:  # Don't log frequent tick data
                self.logger.debug(f"Received {msg_type}: {data}")
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse message: {e}")
        except Exception as e:
            log_error(self.logger, e, "handling message")
    
    def _get_next_request_id(self) -> str:
        """Generate next request ID"""
        self.request_id_counter += 1
        return str(self.request_id_counter)
    
    async def _send_request(self, request: Dict, timeout: float = 10.0) -> Optional[DerivResponse]:
        """Send a request and wait for response"""
        if not self.websocket or self.connection_state not in [ConnectionState.CONNECTED, ConnectionState.AUTHENTICATED]:
            self.logger.error("WebSocket not connected")
            return None
        
        req_id = self._get_next_request_id()
        request['req_id'] = req_id
        
        # Create future for response
        future = asyncio.Future()
        self.pending_requests[req_id] = future
        
        try:
            # Send request
            self.logger.debug(f"Sending request {req_id}: {request}")
            await self.websocket.send(json.dumps(request))

            # Wait for response
            self.logger.debug(f"Waiting for response to request {req_id}")
            response = await asyncio.wait_for(future, timeout=timeout)
            self.logger.debug(f"Received response for request {req_id}")
            return response
            
        except asyncio.TimeoutError:
            self.logger.error(f"Request {req_id} timed out")
            self.pending_requests.pop(req_id, None)
            return None
        except Exception as e:
            log_error(self.logger, e, f"sending request {req_id}")
            self.pending_requests.pop(req_id, None)
            return None
    
    def send_request_sync(self, request: Dict, timeout: float = 10.0) -> Optional[DerivResponse]:
        """Send a request synchronously (thread-safe)"""
        if not self.loop:
            self.logger.error("Event loop not running")
            return None
        
        future = asyncio.run_coroutine_threadsafe(
            self._send_request(request, timeout), 
            self.loop
        )
        
        try:
            return future.result(timeout=timeout)
        except Exception as e:
            log_error(self.logger, e, "sending sync request")
            return None
    
    def register_message_handler(self, msg_type: str, handler: Callable[[DerivResponse], None]):
        """Register a handler for specific message types"""
        self.message_handlers[msg_type] = handler
    
    def is_connected(self) -> bool:
        """Check if client is connected"""
        return self.connection_state in [ConnectionState.CONNECTED, ConnectionState.AUTHENTICATED]
    
    def is_authenticated_user(self) -> bool:
        """Check if client is authenticated"""
        return self.connection_state == ConnectionState.AUTHENTICATED and self.is_authenticated
    
    def stop(self):
        """Stop the WebSocket client"""
        if self.loop:
            asyncio.run_coroutine_threadsafe(self._close_connection(), self.loop)
        
        if self.loop_thread and self.loop_thread.is_alive():
            self.loop_thread.join(timeout=5)
    
    async def _close_connection(self):
        """Close the WebSocket connection"""
        if self.websocket:
            await self.websocket.close()
            self.websocket = None
        self.connection_state = ConnectionState.DISCONNECTED

    # API Methods for trading operations
    def get_active_symbols(self) -> Optional[DerivResponse]:
        """Get list of active trading symbols"""
        return self.send_request_sync({"active_symbols": "brief"})

    def get_contracts_for_symbol(self, symbol: str) -> Optional[DerivResponse]:
        """Get available contracts for a symbol"""
        return self.send_request_sync({"contracts_for": symbol})

    def get_proposal(self, contract_type: str, symbol: str, duration: int,
                    duration_unit: str, stake: float, currency: str = "USD") -> Optional[DerivResponse]:
        """Get a price proposal for a binary option"""
        request = {
            "proposal": 1,
            "amount": stake,
            "basis": "stake",
            "contract_type": contract_type,
            "currency": currency,
            "duration": duration,
            "duration_unit": duration_unit,
            "symbol": symbol
        }
        return self.send_request_sync(request)

    def buy_contract(self, proposal_id: str, price: float) -> Optional[DerivResponse]:
        """Buy a contract using proposal ID"""
        request = {
            "buy": proposal_id,
            "price": price
        }
        return self.send_request_sync(request)

    def sell_contract(self, contract_id: str, price: float) -> Optional[DerivResponse]:
        """Sell an open contract"""
        request = {
            "sell": contract_id,
            "price": price
        }
        return self.send_request_sync(request)

    def get_portfolio(self) -> Optional[DerivResponse]:
        """Get current portfolio of open contracts"""
        return self.send_request_sync({"portfolio": 1})

    def get_balance(self) -> Optional[DerivResponse]:
        """Get account balance"""
        return self.send_request_sync({"balance": 1})

    def subscribe_to_ticks(self, symbol: str, handler: Callable = None) -> Optional[str]:
        """Subscribe to real-time tick data"""
        request = {"ticks": symbol, "subscribe": 1}

        if handler:
            self.register_message_handler("tick", handler)

        response = self.send_request_sync(request)
        if response and not response.error:
            subscription_id = response.data.get('subscription', {}).get('id')
            if subscription_id:
                self.active_subscriptions[subscription_id] = {
                    'type': 'ticks',
                    'symbol': symbol,
                    'handler': handler
                }
            return subscription_id
        return None

    def subscribe_to_candles(self, symbol: str, granularity: int = 60, handler: Callable = None) -> Optional[str]:
        """Subscribe to real-time candle data"""
        request = {
            "ticks_history": symbol,
            "adjust_start_time": 1,
            "count": 1,
            "end": "latest",
            "granularity": granularity,
            "style": "candles",
            "subscribe": 1
        }

        if handler:
            self.register_message_handler("candles", handler)

        response = self.send_request_sync(request)
        if response and not response.error:
            subscription_id = response.data.get('subscription', {}).get('id')
            if subscription_id:
                self.active_subscriptions[subscription_id] = {
                    'type': 'candles',
                    'symbol': symbol,
                    'granularity': granularity,
                    'handler': handler
                }
            return subscription_id
        return None

    def unsubscribe(self, subscription_id: str) -> Optional[DerivResponse]:
        """Unsubscribe from a data stream"""
        request = {"forget": subscription_id}
        response = self.send_request_sync(request)

        if response and not response.error:
            self.active_subscriptions.pop(subscription_id, None)

        return response

    def get_tick_history(self, symbol: str, count: int = 100) -> Optional[DerivResponse]:
        """Get historical tick data"""
        request = {
            "ticks_history": symbol,
            "adjust_start_time": 1,
            "count": count,
            "end": "latest",
            "start": 1
        }
        return self.send_request_sync(request)

    def get_candle_history(self, symbol: str, granularity: int = 60, count: int = 50) -> Optional[DerivResponse]:
        """Get historical candle data"""
        request = {
            "ticks_history": symbol,
            "adjust_start_time": 1,
            "count": count,
            "end": "latest",
            "granularity": granularity,
            "start": 1,
            "style": "candles"
        }
        return self.send_request_sync(request)
