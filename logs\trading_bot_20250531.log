2025-05-31 00:00:49 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:00:49 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:00:59 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:00:59 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:01:02 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 238, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-31 00:01:02 | DerivAPI | ERROR | Request 2 timed out
2025-05-31 00:01:02 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:01:02 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:01:07 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:01:07 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:01:07 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:01:07 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-31 00:01:07 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:01:07 | DerivMarketData | ERROR | Failed to get tick history for R_10: No response
2025-05-31 00:01:07 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:01:12 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:02:42 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:02:42 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:02:52 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:02:52 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:02:53 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:02:53 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:02:58 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:02:58 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:02:58 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:02:58 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-31 00:02:58 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:02:58 | DerivMarketData | ERROR | Failed to get tick history for R_10: No response
2025-05-31 00:02:58 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:03:03 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:03:36 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:12 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:12 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:04:22 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:04:22 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:04:23 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:23 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:04:41 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:42 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:04:52 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:04:52 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:04:52 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:52 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:04:57 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:57 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:04:57 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:04:57 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-31 00:04:57 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:04:57 | DerivMarketData | ERROR | Failed to get tick history for R_10: No response
2025-05-31 00:04:57 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:05:02 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:05:36 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:05:36 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:05:46 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:05:46 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:05:46 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:05:47 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:05:51 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:05:51 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:05:52 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:05:57 | DerivAPI | ERROR | Request 3 timed out
2025-05-31 00:05:57 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:06:02 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:06:02 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:06:02 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:06:02 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:06:02 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:06:02 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:06:07 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:08:04 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:08:05 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:08:15 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:08:15 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:08:15 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:08:15 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:08:20 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:08:20 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:08:20 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:08:25 | DerivAPI | ERROR | Request 3 timed out
2025-05-31 00:08:25 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:08:30 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:08:30 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:08:30 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:08:31 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:08:31 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:08:31 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:08:36 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:14:19 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:14:19 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:14:19 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:14:34 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:14:34 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:14:34 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:14:35 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:14:35 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:14:39 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:14:39 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:14:40 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:14:40 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:14:50 | DerivAPI | ERROR | Request 3 timed out
2025-05-31 00:14:50 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:14:50 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 252, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-31 00:14:50 | DerivAPI | ERROR | Request 2 timed out
2025-05-31 00:14:50 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-31 00:14:55 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:14:55 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:14:55 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:14:55 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:14:55 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:14:55 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:15:00 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:15:38 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:15:39 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:15:39 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:15:54 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:15:54 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:15:55 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:15:56 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:15:56 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:16:23 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:16:23 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:16:23 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:16:38 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:16:38 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:16:39 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:16:40 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:16:40 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:17:24 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:17:25 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:17:25 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:17:37 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:17:37 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:17:41 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:17:41 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:17:41 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:18:13 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:18:13 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:18:13 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:18:26 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:18:37 | DerivAPI | ERROR | Request 2 timed out
2025-05-31 00:18:39 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 266, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-31 00:18:39 | DerivAPI | ERROR | Request 3 timed out
2025-05-31 00:18:48 | DerivAPI | ERROR | Request 4 timed out
2025-05-31 00:18:49 | DerivAPI | ERROR | Authentication failed: Could not verify authentication
2025-05-31 00:18:49 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:18:49 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:18:49 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:19:34 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:19:34 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:19:34 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:19:36 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:19:37 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:19:37 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:19:37 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:19:39 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:19:53 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:19:54 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:19:54 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:19:56 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:19:57 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:19:57 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:19:57 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:19:59 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:20:01 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:20:02 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:20:02 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:20:02 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:20:04 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:20:04 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:20:04 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:20:04 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:20:05 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:20:05 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:20:07 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:20:09 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:21:37 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:21:37 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:21:37 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:21:39 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:21:40 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:21:41 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:21:41 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:21:43 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:21:45 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:21:45 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:21:45 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:21:45 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:21:47 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:21:48 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:21:48 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:21:48 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:21:48 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:21:48 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:21:50 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:21:53 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:25:38 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:25:39 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:25:39 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:25:41 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:25:42 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:25:42 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:25:42 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:25:44 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:25:47 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:25:47 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:25:47 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:25:47 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:25:49 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:25:49 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:25:49 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:25:49 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:25:50 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:25:50 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:25:52 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:25:54 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:27:11 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:27:11 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:27:11 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:27:11 | BinaryOptionsTrader | ERROR | Failed to start API client
2025-05-31 00:28:30 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:28:30 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:28:30 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:28:32 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:28:33 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:28:33 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:28:33 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:28:36 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:28:38 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:28:38 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:28:38 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:28:38 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:28:40 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:28:40 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:28:40 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:28:40 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:28:41 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:28:41 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:28:43 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:28:45 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:29:26 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:29:26 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:29:26 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:29:27 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:29:27 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:29:27 | BinaryOptionsTrader | WARNING | Not authenticated - trading will be limited
2025-05-31 00:29:27 | BinaryOptionsTrader | INFO | Binary options trader started successfully
2025-05-31 00:29:27 | BinaryOptionsBot | INFO | Starting binary options analysis for R_10
2025-05-31 00:29:27 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:29:27 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:29:27 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:29:27 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:29:27 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:29:27 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:29:29 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:29:29 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:29:29 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:29:32 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:29:32 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:29:32 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:29:32 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:29:34 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:29:37 | BinaryOptionsTrader | INFO | Binary options trader stopped
2025-05-31 00:29:37 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:29:37 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:29:37 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:29:39 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:29:42 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:30:17 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:30:17 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:30:17 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:30:17 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:30:17 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:30:17 | BinaryOptionsTrader | WARNING | Not authenticated - trading will be limited
2025-05-31 00:30:17 | BinaryOptionsTrader | INFO | Binary options trader started successfully
2025-05-31 00:30:17 | BinaryOptionsBot | INFO | Starting binary options analysis for R_10
2025-05-31 00:30:17 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:30:17 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:30:17 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:30:17 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:30:18 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:30:18 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:30:19 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:30:19 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:30:19 | DerivMarketData | INFO | Retrieved 100 ticks for R_10
2025-05-31 00:30:20 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:30:20 | DerivMarketData | INFO | Retrieved 50 candles for R_10
2025-05-31 00:30:20 | AIAnalyzer | ERROR | [ERROR] in OpenAI API analysis: 'symbol'
Traceback (most recent call last):
  File "E:\Desktop\BINARY\ai_analyzer.py", line 221, in analyze_market_data
    prompt = self._create_analysis_prompt(market_data, additional_context, trade_state_context)
  File "E:\Desktop\BINARY\ai_analyzer.py", line 103, in _create_analysis_prompt
    formatted_data = self._format_market_data(market_data)
  File "E:\Desktop\BINARY\ai_analyzer.py", line 50, in _format_market_data
    formatted_data.append(f"Current Quote for {quote['symbol']}:")
KeyError: 'symbol'
2025-05-31 00:30:20 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:30:20 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:30:20 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:30:22 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:30:25 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:30:25 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:30:25 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:30:25 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:30:27 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:30:30 | BinaryOptionsTrader | INFO | Binary options trader stopped
2025-05-31 00:30:30 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:30:30 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:30:30 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:30:32 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:30:35 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:31:12 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:31:12 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:31:12 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:31:12 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:31:12 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:31:12 | BinaryOptionsTrader | WARNING | Not authenticated - trading will be limited
2025-05-31 00:31:12 | BinaryOptionsTrader | INFO | Binary options trader started successfully
2025-05-31 00:31:12 | BinaryOptionsBot | INFO | Starting binary options analysis for R_10
2025-05-31 00:31:13 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:31:13 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:31:13 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:31:13 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:31:13 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:31:13 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:31:15 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:31:15 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:31:15 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:31:15 | DerivMarketData | INFO | Retrieved 100 ticks for R_10
2025-05-31 00:31:15 | DerivMarketData | INFO | Retrieved 50 candles for R_10
2025-05-31 00:31:15 | AIAnalyzer | INFO | [AI] Analyzing market data for R_10
2025-05-31 00:31:25 | AIAnalyzer | INFO | [AI] Analysis complete: CALL (confidence: 70.00%)
2025-05-31 00:31:25 | SignalGenerator | INFO | [STATE_CHANGE] R_10: NO_POSITION → CALL_POSITION
2025-05-31 00:31:25 | SignalGenerator | INFO | [EXPORT] CALL signal exported to signal_call_R_10_20250531_003125.json
2025-05-31 00:31:25 | SignalGenerator | INFO | [SIGNAL] CALL | R_10 | Confidence: 70.00%
2025-05-31 00:31:25 | SignalGenerator | INFO | [REASONING] The price of the R_10 index has been showing a slight upward trend in the last 10 periods. The lows are gradually increasing, indicating a bullish momentum. Although the price changes are not significant, the consistent upward trend suggests a higher probability of the price continuing to rise in the short term. However, the volatility is relatively low, which reduces the confidence level of the prediction.
2025-05-31 00:31:25 | BinaryOptionsTrader | INFO | Placed CALL trade on R_10: Stake=$1.0, Payout=$1.95, Entry=$6462.291
2025-05-31 00:31:25 | BinaryOptionsTrader | INFO | Contract 283308573268 expired: LOST, P&L=$-1.00
2025-05-31 00:31:25 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:31:26 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:31:26 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:31:28 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:31:30 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:31:30 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:31:31 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:31:31 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:31:33 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:31:35 | BinaryOptionsTrader | INFO | Binary options trader stopped
2025-05-31 00:31:35 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:31:36 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:31:36 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:31:38 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:31:40 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:33:16 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:33:16 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:33:16 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:33:16 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:33:16 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:33:16 | BinaryOptionsTrader | WARNING | Not authenticated - trading will be limited
2025-05-31 00:33:16 | BinaryOptionsTrader | INFO | Binary options trader started successfully
2025-05-31 00:33:16 | BinaryOptionsBot | INFO | Starting binary options analysis for R_10
2025-05-31 00:33:16 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:33:16 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:33:16 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:33:16 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:33:16 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:33:16 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:33:18 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:33:18 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:33:18 | DerivMarketData | INFO | Retrieved 100 ticks for R_10
2025-05-31 00:33:18 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:33:18 | DerivMarketData | INFO | Retrieved 50 candles for R_10
2025-05-31 00:33:18 | AIAnalyzer | INFO | [AI] Analyzing market data for R_10
2025-05-31 00:33:28 | AIAnalyzer | INFO | [AI] Analysis complete: CALL (confidence: 65.00%)
2025-05-31 00:33:28 | SignalGenerator | INFO | [STATE_CHANGE] R_10: NO_POSITION → CALL_POSITION
2025-05-31 00:33:28 | SignalGenerator | INFO | [EXPORT] CALL signal exported to signal_call_R_10_20250531_003328.json
2025-05-31 00:33:28 | SignalGenerator | INFO | [SIGNAL] CALL | R_10 | Confidence: 65.00%
2025-05-31 00:33:28 | SignalGenerator | INFO | [REASONING] The price has been generally stable with a slight upward trend in the last 10 periods. The highest price in the last period is higher than the opening price in the first period, indicating a potential continuation of the upward trend. However, the volatility is relatively low, which reduces the confidence level.
2025-05-31 00:33:28 | BinaryOptionsTrader | INFO | Placed CALL trade on R_10: Stake=$1.0, Payout=$1.95, Entry=$6461.161
2025-05-31 00:33:28 | BinaryOptionsTrader | INFO | Contract 283308666768 expired: LOST, P&L=$-1.00
2025-05-31 00:33:28 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:33:29 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:33:29 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:33:31 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:33:33 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:33:33 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:33:34 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:33:34 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:33:36 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:33:38 | BinaryOptionsTrader | INFO | Binary options trader stopped
2025-05-31 00:33:38 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:33:39 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:33:39 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:33:41 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:33:43 | DerivMarketData | INFO | Deriv market data provider stopped
