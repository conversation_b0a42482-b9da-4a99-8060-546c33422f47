2025-05-31 00:00:49 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:00:49 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:00:59 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:00:59 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:01:02 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 238, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-31 00:01:02 | DerivAPI | ERROR | Request 2 timed out
2025-05-31 00:01:02 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:01:02 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:01:07 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:01:07 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:01:07 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:01:07 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-31 00:01:07 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:01:07 | DerivMarketData | ERROR | Failed to get tick history for R_10: No response
2025-05-31 00:01:07 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:01:12 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:02:42 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:02:42 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:02:52 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:02:52 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:02:53 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:02:53 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:02:58 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:02:58 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:02:58 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:02:58 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-31 00:02:58 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:02:58 | DerivMarketData | ERROR | Failed to get tick history for R_10: No response
2025-05-31 00:02:58 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:03:03 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:03:36 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:12 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:12 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:04:22 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:04:22 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:04:23 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:23 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:04:41 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:42 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:04:52 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:04:52 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:04:52 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:52 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:04:57 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:04:57 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:04:57 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:04:57 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-31 00:04:57 | DerivAPI | ERROR | WebSocket not connected
2025-05-31 00:04:57 | DerivMarketData | ERROR | Failed to get tick history for R_10: No response
2025-05-31 00:04:57 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:05:02 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:05:36 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:05:36 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:05:46 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:05:46 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:05:46 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:05:47 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:05:51 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:05:51 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:05:52 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:05:57 | DerivAPI | ERROR | Request 3 timed out
2025-05-31 00:05:57 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:06:02 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:06:02 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:06:02 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:06:02 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:06:02 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:06:02 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:06:07 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:08:04 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:08:05 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:08:15 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:08:15 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:08:15 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:08:15 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:08:20 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:08:20 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:08:20 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:08:25 | DerivAPI | ERROR | Request 3 timed out
2025-05-31 00:08:25 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:08:30 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:08:30 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:08:30 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:08:31 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:08:31 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:08:31 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:08:36 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:14:19 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:14:19 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:14:19 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:14:34 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:14:34 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:14:34 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:14:35 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:14:35 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:14:39 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:14:39 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:14:40 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:14:40 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:14:50 | DerivAPI | ERROR | Request 3 timed out
2025-05-31 00:14:50 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:14:50 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 252, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-31 00:14:50 | DerivAPI | ERROR | Request 2 timed out
2025-05-31 00:14:50 | DerivMarketData | ERROR | Failed to get active symbols: No response
2025-05-31 00:14:55 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:14:55 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:14:55 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:14:55 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:14:55 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:14:55 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:15:00 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:15:38 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:15:39 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:15:39 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:15:54 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:15:54 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:15:55 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:15:56 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:15:56 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:16:23 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:16:23 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:16:23 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:16:38 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:16:38 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:16:39 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:16:40 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:16:40 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:17:24 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:17:25 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:17:25 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:17:37 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:17:37 | DerivAPI | ERROR | Authentication failed: No response
2025-05-31 00:17:41 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:17:41 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:17:41 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:18:13 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:18:13 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:18:13 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:18:26 | DerivAPI | ERROR | Request 1 timed out
2025-05-31 00:18:37 | DerivAPI | ERROR | Request 2 timed out
2025-05-31 00:18:39 | DerivAPI | ERROR | [ERROR] in sending sync request: 
Traceback (most recent call last):
  File "E:\Desktop\BINARY\deriv_api.py", line 266, in send_request_sync
    return future.result(timeout=timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\concurrent\futures\_base.py", line 447, in result
    raise TimeoutError()
concurrent.futures._base.TimeoutError
2025-05-31 00:18:39 | DerivAPI | ERROR | Request 3 timed out
2025-05-31 00:18:48 | DerivAPI | ERROR | Request 4 timed out
2025-05-31 00:18:49 | DerivAPI | ERROR | Authentication failed: Could not verify authentication
2025-05-31 00:18:49 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:18:49 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:18:49 | DerivAPI | INFO | Starting authentication...
2025-05-31 00:19:34 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:19:34 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:19:34 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:19:36 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:19:37 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:19:37 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:19:37 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:19:39 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:19:53 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:19:54 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:19:54 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:19:56 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:19:57 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:19:57 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:19:57 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:19:59 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:20:01 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:20:02 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:20:02 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:20:02 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:20:04 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:20:04 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:20:04 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:20:04 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:20:05 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:20:05 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:20:07 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:20:09 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:21:37 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:21:37 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:21:37 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:21:39 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:21:40 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:21:41 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:21:41 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:21:43 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:21:45 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:21:45 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:21:45 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:21:45 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:21:47 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:21:48 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:21:48 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:21:48 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:21:48 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:21:48 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:21:50 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:21:53 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:25:38 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:25:39 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:25:39 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:25:41 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:25:42 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:25:42 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:25:42 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:25:44 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:25:47 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:25:47 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:25:47 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:25:47 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:25:49 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:25:49 | DerivMarketData | INFO | Retrieved 34 active symbols
2025-05-31 00:25:49 | DerivMarketData | INFO | Retrieved 10 ticks for R_10
2025-05-31 00:25:49 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:25:50 | DerivAPI | INFO | Connected to Deriv API
2025-05-31 00:25:50 | DerivAPI | INFO | Authenticating with Deriv API...
2025-05-31 00:25:52 | DerivAPI | INFO | Authentication request sent - API ready for use
2025-05-31 00:25:54 | DerivMarketData | INFO | Deriv market data provider stopped
2025-05-31 00:27:11 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:27:11 | DerivMarketData | INFO | Deriv market data provider started
2025-05-31 00:27:11 | DerivAPI | INFO | Connecting to Deriv API: wss://ws.derivws.com/websockets/v3?app_id=1089
2025-05-31 00:27:11 | BinaryOptionsTrader | ERROR | Failed to start API client
